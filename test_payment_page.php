<?php
/**
 * 测试支付页面
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

// 获取订单号
$order_no = isset($_GET['order_no']) ? trim($_GET['order_no']) : '';

if (empty($order_no)) {
    echo "<script>alert('订单号不能为空！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 获取订单信息
$order_table = $DB->table('payment_orders');
$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if (!$order) {
    echo "<script>alert('订单不存在！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 检查订单是否已过期
if ($order['expire_time'] < time()) {
    echo "<script>alert('订单已过期，请重新提交！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 检查订单是否已支付
if ($order['status'] == 1) {
    echo "<script>alert('订单已支付成功！'); window.location.href = '?mod=index';</script>";
    exit;
}

// 如果没有二维码数据，尝试生成
if (empty($order['qr_code'])) {
    if (file_exists(MOD_PATH.'payment_functions.php')) {
        require_once(MOD_PATH.'payment_functions.php');
        
        if (function_exists('get_payment_price') && function_exists('create_payment')) {
            $price_info = get_payment_price($order['payment_type']);
            if ($price_info) {
                $order_data = array(
                    'order_id' => $order['order_id'],
                    'order_no' => $order['order_no'],
                    'amount' => $order['amount'],
                    'price_info' => $price_info
                );
                
                $payment_result = create_payment($order_data);
                if ($payment_result && isset($payment_result['ewm'])) {
                    $order['qr_code'] = $payment_result['ewm'];
                }
            }
        }
    }
}

// 计算剩余时间
$remaining_time = $order['expire_time'] - time();
?>
<!DOCTYPE HTML>
<html>
<head>
<title>支付页面 - 测试</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.container { max-width: 800px; margin: 0 auto; }
.payment-info { background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
.payment-qr { text-align: center; background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
.order-table { width: 100%; border-collapse: collapse; }
.order-table td { padding: 8px; border-bottom: 1px solid #eee; }
.order-table td:first-child { font-weight: bold; width: 120px; }
.price { color: #e74c3c; font-size: 18px; font-weight: bold; }
.countdown { color: #e74c3c; margin-top: 10px; }
.loading { padding: 40px; color: #666; }
</style>
</head>
<body>

<div class="container">
    <h2>💳 支付页面</h2>
    
    <div class="payment-info">
        <h3>📋 订单信息</h3>
        <table class="order-table">
            <tr>
                <td>订单号：</td>
                <td><?php echo htmlspecialchars($order['order_no']); ?></td>
            </tr>
            <tr>
                <td>网站名称：</td>
                <td><?php echo htmlspecialchars($order['web_name']); ?></td>
            </tr>
            <tr>
                <td>网站地址：</td>
                <td><?php echo htmlspecialchars($order['web_url']); ?></td>
            </tr>
            <tr>
                <td>支付金额：</td>
                <td class="price">¥<?php echo $order['amount']; ?></td>
            </tr>
            <tr>
                <td>订单状态：</td>
                <td><?php echo $order['status'] == 0 ? '待支付' : '已支付'; ?></td>
            </tr>
        </table>
        
        <div class="countdown">
            订单将在 <span id="countdown-timer"><?php echo gmdate('i:s', $remaining_time); ?></span> 后过期
        </div>
    </div>
    
    <div class="payment-qr">
        <h3>📱 微信扫码支付</h3>
        <div id="qr-code-container">
            <?php if (!empty($order['qr_code'])): ?>
                <div id="qrcode"></div>
                <p>请使用微信扫描上方二维码完成支付</p>
                <p style="font-size: 12px; color: #666;">二维码数据: <?php echo htmlspecialchars(substr($order['qr_code'], 0, 50)) . '...'; ?></p>
            <?php else: ?>
                <div class="loading">
                    <p>正在生成支付二维码...</p>
                    <p style="color: red;">调试信息：二维码数据为空</p>
                    <p>订单ID: <?php echo $order['order_id']; ?></p>
                    <p>支付类型: <?php echo $order['payment_type']; ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div style="margin-top: 20px; text-align: center;">
        <a href="debug_payment.php?order_no=<?php echo $order['order_no']; ?>">调试订单信息</a> |
        <a href="create_test_order.php">创建新订单</a> |
        <a href="?mod=quicksubmit">返回提交页面</a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 生成二维码
    <?php if (!empty($order['qr_code'])): ?>
    const qrCodeData = <?php echo json_encode($order['qr_code']); ?>;
    console.log('二维码数据:', qrCodeData);
    
    if (qrCodeData && document.getElementById('qrcode')) {
        QRCode.toCanvas(document.getElementById('qrcode'), qrCodeData, {
            width: 200,
            height: 200,
            margin: 2
        }, function (error) {
            if (error) {
                console.error('二维码生成失败:', error);
                document.getElementById('qr-code-container').innerHTML = '<div class="loading">二维码生成失败: ' + error + '</div>';
            } else {
                console.log('二维码生成成功');
            }
        });
    }
    <?php endif; ?>
    
    // 倒计时
    let remainingTime = <?php echo $remaining_time; ?>;
    const countdownTimer = document.getElementById('countdown-timer');
    
    function updateCountdown() {
        if (remainingTime <= 0) {
            alert('订单已过期，请重新提交！');
            window.location.href = '?mod=quicksubmit';
            return;
        }
        
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        countdownTimer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        remainingTime--;
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    // 轮询查询支付状态
    const orderNo = <?php echo json_encode($order['order_no']); ?>;
    let pollCount = 0;
    
    function checkPaymentStatus() {
        if (pollCount >= 60) return; // 最多轮询5分钟
        pollCount++;
        
        fetch('?mod=payment_check&order_no=' + orderNo)
            .then(response => response.json())
            .then(data => {
                console.log('支付状态查询结果:', data);
                if (data.success && data.paid) {
                    alert('支付成功！您的网站已提交审核。');
                    window.location.href = '?mod=index';
                }
            })
            .catch(error => {
                console.error('查询支付状态失败:', error);
            });
    }
    
    // 每5秒查询一次支付状态
    setInterval(checkPaymentStatus, 5000);
});
</script>

</body>
</html>
