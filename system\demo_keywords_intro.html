<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI生成关键词和简介 - 演示页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group { 
            margin-bottom: 20px; 
        }
        label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: bold; 
            color: #555;
        }
        input[type="text"], textarea { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        .btn { 
            padding: 12px 24px; 
            background: #007cba; 
            color: white; 
            border: none; 
            border-radius: 4px;
            cursor: pointer; 
            font-size: 16px;
            font-weight: bold;
        }
        .btn:hover { 
            background: #005a87; 
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result { 
            margin-top: 30px; 
            padding: 20px; 
            border: 1px solid #ddd; 
            background: #f9f9f9; 
            border-radius: 4px;
        }
        .status { 
            margin-left: 15px; 
            font-weight: bold;
        }
        .success { color: #009900; }
        .error { color: #cc0000; }
        .loading { color: #0066cc; }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-left: 4px solid #007cba;
        }
        .result-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .result-content {
            color: #666;
            line-height: 1.5;
        }
        .example {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .example h3 {
            margin-top: 0;
            color: #007cba;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI生成关键词和简介</h1>
        
        <div class="example">
            <h3>功能说明</h3>
            <p>这个工具可以根据网站的Meta信息（标题、关键词、描述）自动生成适合的SEO关键词和网站简介。</p>
            <p><strong>使用方法：</strong>输入网站域名和Meta信息，点击生成按钮即可。</p>
        </div>
        
        <form id="demoForm">
            <div class="form-group">
                <label for="url">🌐 网站域名 *</label>
                <input type="text" id="url" name="url" placeholder="例：www.baidu.com" value="www.baidu.com">
            </div>
            
            <div class="form-group">
                <label for="meta_title">📝 网站标题（可选）</label>
                <input type="text" id="meta_title" name="meta_title" placeholder="网站标题" value="百度一下，你就知道">
            </div>
            
            <div class="form-group">
                <label for="meta_keywords">🏷️ Meta关键词（可选）</label>
                <input type="text" id="meta_keywords" name="meta_keywords" placeholder="关键词1,关键词2" value="搜索引擎,百度,搜索">
            </div>
            
            <div class="form-group">
                <label for="meta_description">📄 Meta描述（可选）</label>
                <textarea id="meta_description" name="meta_description" placeholder="网站描述">全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。</textarea>
            </div>
            
            <button type="button" class="btn" id="generateBtn" onclick="generateContent()">
                🚀 生成AI关键词和简介
            </button>
            <span id="status" class="status"></span>
        </form>
        
        <div id="result" class="result" style="display: none;">
            <h3>✨ 生成结果</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        function generateContent() {
            var url = document.getElementById('url').value.trim();
            var metaTitle = document.getElementById('meta_title').value.trim();
            var metaKeywords = document.getElementById('meta_keywords').value.trim();
            var metaDescription = document.getElementById('meta_description').value.trim();
            
            if (!url) {
                alert('请输入网站域名');
                return;
            }
            
            // 禁用按钮，显示加载状态
            var btn = document.getElementById('generateBtn');
            btn.disabled = true;
            btn.textContent = '⏳ 生成中...';
            document.getElementById('status').innerHTML = '<span class="loading">正在调用AI生成中，请稍候...</span>';
            document.getElementById('result').style.display = 'none';
            
            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'generate_keywords_intro.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    // 恢复按钮状态
                    btn.disabled = false;
                    btn.textContent = '🚀 生成AI关键词和简介';
                    
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.status === 'success') {
                                document.getElementById('status').innerHTML = '<span class="success">✅ 生成成功！</span>';
                                
                                var resultHtml = '';
                                if (response.keywords) {
                                    resultHtml += '<div class="result-item">';
                                    resultHtml += '<div class="result-label">🏷️ 生成的关键词：</div>';
                                    resultHtml += '<div class="result-content">' + response.keywords + '</div>';
                                    resultHtml += '</div>';
                                }
                                
                                if (response.intro) {
                                    resultHtml += '<div class="result-item">';
                                    resultHtml += '<div class="result-label">📝 生成的简介：</div>';
                                    resultHtml += '<div class="result-content">' + response.intro + '</div>';
                                    resultHtml += '</div>';
                                }
                                
                                if (response.raw_content) {
                                    resultHtml += '<div class="result-item">';
                                    resultHtml += '<div class="result-label">🤖 AI原始返回：</div>';
                                    resultHtml += '<div class="result-content"><pre style="white-space: pre-wrap; font-family: inherit;">' + response.raw_content + '</pre></div>';
                                    resultHtml += '</div>';
                                }
                                
                                document.getElementById('result-content').innerHTML = resultHtml;
                                document.getElementById('result').style.display = 'block';
                            } else {
                                document.getElementById('status').innerHTML = '<span class="error">❌ 生成失败：' + response.message + '</span>';
                            }
                        } catch (e) {
                            document.getElementById('status').innerHTML = '<span class="error">❌ 解析错误：' + e.message + '</span>';
                            console.log('JSON解析错误:', e);
                            console.log('原始响应:', xhr.responseText);
                        }
                    } else {
                        document.getElementById('status').innerHTML = '<span class="error">❌ 请求错误(' + xhr.status + ')，请重试</span>';
                        console.log('HTTP错误:', xhr.status, xhr.statusText);
                        console.log('响应内容:', xhr.responseText);
                    }
                }
            };
            
            // 构建POST数据
            var postData = 'url=' + encodeURIComponent(url) +
                          '&meta_title=' + encodeURIComponent(metaTitle) +
                          '&meta_keywords=' + encodeURIComponent(metaKeywords) +
                          '&meta_description=' + encodeURIComponent(metaDescription);
            
            xhr.send(postData);
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI关键词生成功能已加载完成');
        });
    </script>
</body>
</html>
