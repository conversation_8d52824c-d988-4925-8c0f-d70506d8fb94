<?php
/**
 * 调试支付页面问题
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

echo "<h2>调试支付页面问题</h2>";

// 获取订单号
$order_no = isset($_GET['order_no']) ? trim($_GET['order_no']) : '202508076368360366813';

echo "<h3>1. 检查订单信息</h3>";
echo "<p>订单号: {$order_no}</p>";

// 获取订单信息
$order_table = $DB->table('payment_orders');
echo "<p>订单表名: {$order_table}</p>";

$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if ($order) {
    echo "<p style='color: green;'>✓ 订单存在</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    foreach ($order as $key => $value) {
        echo "<tr><td><strong>{$key}</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>2. 检查订单状态</h3>";
    echo "<p>订单状态: " . ($order['status'] == 0 ? '待支付' : ($order['status'] == 1 ? '已支付' : '已取消')) . "</p>";
    echo "<p>过期时间: " . date('Y-m-d H:i:s', $order['expire_time']) . "</p>";
    echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>是否过期: " . ($order['expire_time'] < time() ? '是' : '否') . "</p>";
    
    echo "<h3>3. 检查二维码数据</h3>";
    if (!empty($order['qr_code'])) {
        echo "<p style='color: green;'>✓ 二维码数据存在</p>";
        echo "<p>二维码内容: " . htmlspecialchars($order['qr_code']) . "</p>";
        
        // 生成二维码
        echo "<h4>生成二维码:</h4>";
        echo "<div id='qrcode'></div>";
        echo "<script src='https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'></script>";
        echo "<script>
            QRCode.toCanvas(document.getElementById('qrcode'), '" . addslashes($order['qr_code']) . "', {
                width: 200,
                height: 200,
                margin: 2
            }, function (error) {
                if (error) {
                    console.error('二维码生成失败:', error);
                    document.getElementById('qrcode').innerHTML = '<p style=\"color: red;\">二维码生成失败: ' + error + '</p>';
                }
            });
        </script>";
    } else {
        echo "<p style='color: red;'>✗ 二维码数据为空</p>";
        
        // 尝试调用支付接口生成二维码
        echo "<h4>尝试生成二维码:</h4>";
        
        if (file_exists(MOD_PATH.'payment_functions.php')) {
            require_once(MOD_PATH.'payment_functions.php');
            
            if (function_exists('get_payment_price')) {
                $price_info = get_payment_price($order['payment_type']);
                if ($price_info) {
                    $order_data = array(
                        'order_id' => $order['order_id'],
                        'order_no' => $order['order_no'],
                        'amount' => $order['amount'],
                        'price_info' => $price_info
                    );
                    
                    if (function_exists('create_payment')) {
                        $payment_result = create_payment($order_data);
                        if ($payment_result && isset($payment_result['ewm'])) {
                            echo "<p style='color: green;'>✓ 支付接口调用成功</p>";
                            echo "<p>新的二维码数据: " . htmlspecialchars($payment_result['ewm']) . "</p>";
                            
                            // 生成二维码
                            echo "<div id='qrcode2'></div>";
                            echo "<script>
                                QRCode.toCanvas(document.getElementById('qrcode2'), '" . addslashes($payment_result['ewm']) . "', {
                                    width: 200,
                                    height: 200,
                                    margin: 2
                                });
                            </script>";
                        } else {
                            echo "<p style='color: red;'>✗ 支付接口调用失败</p>";
                            
                            // 手动测试支付接口
                            echo "<h5>手动测试支付接口:</h5>";
                            $api_url = 'https://api.rcku.cn/wxpay/pay';
                            $params = array(
                                'price' => $order['amount'],
                                'orderid' => $order['order_no'],
                                'description' => $price_info['description']
                            );
                            $request_url = $api_url . '?' . http_build_query($params);
                            echo "<p>请求URL: <a href='{$request_url}' target='_blank'>{$request_url}</a></p>";
                        }
                    }
                }
            }
        }
    }
    
    echo "<h3>4. 检查支付页面模板</h3>";
    $template_path = ROOT_PATH . 'themes/default/payment.html';
    if (file_exists($template_path)) {
        echo "<p style='color: green;'>✓ 支付页面模板存在</p>";
    } else {
        echo "<p style='color: red;'>✗ 支付页面模板不存在: {$template_path}</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ 订单不存在</p>";
    
    // 显示所有订单
    echo "<h3>所有订单:</h3>";
    $query = $DB->query("SELECT order_no, web_name, amount, status, create_time FROM $order_table ORDER BY create_time DESC LIMIT 10");
    if ($DB->num_rows($query) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>订单号</th><th>网站名称</th><th>金额</th><th>状态</th><th>创建时间</th></tr>";
        while ($row = $DB->fetch_array($query)) {
            echo "<tr>";
            echo "<td>{$row['order_no']}</td>";
            echo "<td>{$row['web_name']}</td>";
            echo "<td>¥{$row['amount']}</td>";
            echo "<td>" . ($row['status'] == 0 ? '待支付' : '已支付') . "</td>";
            echo "<td>" . date('Y-m-d H:i:s', $row['create_time']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>没有找到任何订单</p>";
    }
}

echo "<hr>";
echo "<p><a href='?mod=payment&order_no={$order_no}'>访问支付页面</a></p>";
echo "<p><a href='create_test_order.php'>创建新的测试订单</a></p>";
?>
