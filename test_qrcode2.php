<!DOCTYPE HTML>
<html>
<head>
<title>测试二维码生成 - 方案2</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-item { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
.qr-container { text-align: center; margin: 20px 0; }
</style>
</head>
<body>

<h2>测试二维码生成 - 方案2</h2>

<div class="test-item">
    <h3>测试1: 微信支付URL</h3>
    <p>数据: weixin://wxpay/bizpayurl?pr=Va3Ldtrz1</p>
    <div class="qr-container">
        <canvas id="qrcode1" width="200" height="200"></canvas>
    </div>
</div>

<div class="test-item">
    <h3>测试2: 普通文本</h3>
    <p>数据: Hello World</p>
    <div class="qr-container">
        <canvas id="qrcode2" width="200" height="200"></canvas>
    </div>
</div>

<div class="test-item">
    <h3>测试3: 网址</h3>
    <p>数据: https://www.95dir.com</p>
    <div class="qr-container">
        <canvas id="qrcode3" width="200" height="200"></canvas>
    </div>
</div>

<div class="test-item">
    <h3>测试4: 在线二维码API</h3>
    <p>数据: weixin://wxpay/bizpayurl?pr=Va3Ldtrz1</p>
    <div class="qr-container">
        <img id="qrcode4" src="" alt="二维码" style="border: 1px solid #ddd;">
    </div>
</div>

<!-- 尝试多个CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
<script>
// 备用CDN
if (typeof QRCode === 'undefined') {
    console.log('主CDN失败，尝试备用CDN');
    const script = document.createElement('script');
    script.src = 'https://cdn.bootcdn.net/ajax/libs/qrcode/1.5.3/qrcode.min.js';
    script.onload = function() {
        console.log('备用CDN加载成功');
        generateQRCodes();
    };
    script.onerror = function() {
        console.log('备用CDN也失败，使用在线API');
        useOnlineAPI();
    };
    document.head.appendChild(script);
} else {
    console.log('主CDN加载成功');
    document.addEventListener('DOMContentLoaded', generateQRCodes);
}

function generateQRCodes() {
    console.log('开始生成二维码，QRCode类型:', typeof QRCode);
    
    if (typeof QRCode === 'undefined') {
        console.error('QRCode库仍未加载');
        useOnlineAPI();
        return;
    }
    
    // 测试数据
    const testData = [
        { id: 'qrcode1', data: 'weixin://wxpay/bizpayurl?pr=Va3Ldtrz1' },
        { id: 'qrcode2', data: 'Hello World' },
        { id: 'qrcode3', data: 'https://www.95dir.com' }
    ];
    
    testData.forEach(function(test, index) {
        console.log('生成二维码 ' + (index + 1) + ':', test.data);
        
        const canvas = document.getElementById(test.id);
        if (!canvas) {
            console.error('找不到canvas元素:', test.id);
            return;
        }
        
        try {
            QRCode.toCanvas(canvas, test.data, {
                width: 200,
                height: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('二维码 ' + (index + 1) + ' 生成失败:', error);
                    canvas.style.display = 'none';
                    canvas.parentNode.innerHTML += '<p style="color: red;">生成失败: ' + error + '</p>';
                } else {
                    console.log('二维码 ' + (index + 1) + ' 生成成功');
                }
            });
        } catch (e) {
            console.error('二维码 ' + (index + 1) + ' 生成异常:', e);
            canvas.style.display = 'none';
            canvas.parentNode.innerHTML += '<p style="color: red;">生成异常: ' + e.message + '</p>';
        }
    });
    
    // 使用在线API生成第4个二维码
    useOnlineAPI();
}

function useOnlineAPI() {
    console.log('使用在线API生成二维码');
    
    // 使用在线二维码生成API
    const qrData = encodeURIComponent('weixin://wxpay/bizpayurl?pr=Va3Ldtrz1');
    const apiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${qrData}`;
    
    const img = document.getElementById('qrcode4');
    if (img) {
        img.src = apiUrl;
        img.onload = function() {
            console.log('在线API二维码加载成功');
        };
        img.onerror = function() {
            console.error('在线API二维码加载失败');
            img.parentNode.innerHTML += '<p style="color: red;">在线API也失败了</p>';
        };
    }
}
</script>

</body>
</html>
