<!DOCTYPE HTML>
<html>
<head>
<title>测试二维码生成</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-item { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
.qr-container { text-align: center; margin: 20px 0; }
</style>
</head>
<body>

<h2>测试二维码生成</h2>

<div class="test-item">
    <h3>测试1: 微信支付URL</h3>
    <p>数据: weixin://wxpay/bizpayurl?pr=Va3Ldtrz1</p>
    <div class="qr-container">
        <div id="qrcode1"></div>
    </div>
</div>

<div class="test-item">
    <h3>测试2: 普通文本</h3>
    <p>数据: Hello World</p>
    <div class="qr-container">
        <div id="qrcode2"></div>
    </div>
</div>

<div class="test-item">
    <h3>测试3: 网址</h3>
    <p>数据: https://www.95dir.com</p>
    <div class="qr-container">
        <div id="qrcode3"></div>
    </div>
</div>

<div class="test-item">
    <h3>测试4: 另一个微信支付URL</h3>
    <p>数据: weixin://wxpay/bizpayurl?pr=4Jhp5dpz1</p>
    <div class="qr-container">
        <div id="qrcode4"></div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 测试数据
    const testData = [
        { id: 'qrcode1', data: 'weixin://wxpay/bizpayurl?pr=Va3Ldtrz1' },
        { id: 'qrcode2', data: 'Hello World' },
        { id: 'qrcode3', data: 'https://www.95dir.com' },
        { id: 'qrcode4', data: 'weixin://wxpay/bizpayurl?pr=4Jhp5dpz1' }
    ];
    
    testData.forEach(function(test, index) {
        console.log('生成二维码 ' + (index + 1) + ':', test.data);
        
        try {
            QRCode.toCanvas(document.getElementById(test.id), test.data, {
                width: 200,
                height: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('二维码 ' + (index + 1) + ' 生成失败:', error);
                    document.getElementById(test.id).innerHTML = '<p style="color: red;">生成失败: ' + error + '</p>';
                } else {
                    console.log('二维码 ' + (index + 1) + ' 生成成功');
                }
            });
        } catch (e) {
            console.error('二维码 ' + (index + 1) + ' 生成异常:', e);
            document.getElementById(test.id).innerHTML = '<p style="color: red;">生成异常: ' + e.message + '</p>';
        }
    });
});
</script>

</body>
</html>
