<?php
/**
 * 创建测试订单
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
}

echo "<h2>创建测试订单</h2>";

// 测试表单数据
$form_data = array(
    'cate_id' => 1,
    'web_name' => '测试网站',
    'web_url' => 'test.com',
    'web_tags' => '测试,网站',
    'web_intro' => '这是一个测试网站的简介',
    'web_owner' => '测试用户',
    'web_email' => '<EMAIL>'
);

$payment_type = 3; // 快审

if (function_exists('create_payment_order')) {
    // 创建订单
    $order_data = create_payment_order($form_data, $payment_type);
    
    if ($order_data) {
        echo "<p style='color: green;'>✓ 订单创建成功</p>";
        echo "<p>订单号: {$order_data['order_no']}</p>";
        echo "<p>金额: ¥{$order_data['amount']}</p>";
        echo "<p>过期时间: " . date('Y-m-d H:i:s', $order_data['expire_time']) . "</p>";
        
        // 调用支付接口
        if (function_exists('create_payment')) {
            $payment_result = create_payment($order_data);
            if ($payment_result) {
                echo "<p style='color: green;'>✓ 支付接口调用成功</p>";
                echo "<p>二维码数据: {$payment_result['ewm']}</p>";
            } else {
                echo "<p style='color: red;'>✗ 支付接口调用失败</p>";
            }
        }
        
        echo "<hr>";
        echo "<p><a href='?mod=payment&order_no={$order_data['order_no']}' target='_blank'>查看支付页面</a></p>";
        
    } else {
        echo "<p style='color: red;'>✗ 订单创建失败</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 支付函数不存在</p>";
}

// 显示现有订单
echo "<h3>现有订单</h3>";
$order_table = $DB->table('payment_orders');
$query = $DB->query("SELECT * FROM $order_table ORDER BY create_time DESC LIMIT 10");

if ($DB->num_rows($query) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>订单号</th><th>网站名称</th><th>金额</th><th>状态</th><th>创建时间</th><th>操作</th></tr>";
    
    while ($row = $DB->fetch_array($query)) {
        $status_text = $row['status'] == 0 ? '待支付' : ($row['status'] == 1 ? '已支付' : '已取消');
        $create_time = date('Y-m-d H:i:s', $row['create_time']);
        
        echo "<tr>";
        echo "<td>{$row['order_no']}</td>";
        echo "<td>{$row['web_name']}</td>";
        echo "<td>¥{$row['amount']}</td>";
        echo "<td>{$status_text}</td>";
        echo "<td>{$create_time}</td>";
        echo "<td><a href='?mod=payment&order_no={$row['order_no']}' target='_blank'>查看</a></td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>暂无订单</p>";
}

echo "<hr>";
echo "<p><a href='?mod=quicksubmit'>返回快速提交页面</a></p>";
?>
