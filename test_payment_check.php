<?php
/**
 * 测试支付检查接口
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 引入支付处理函数
if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
}

// 获取订单号
$order_no = isset($_GET['order_no']) ? trim($_GET['order_no']) : '';

$response = array(
    'success' => false,
    'paid' => false,
    'message' => '参数错误',
    'debug' => array()
);

if (empty($order_no)) {
    $response['message'] = '订单号为空';
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

$response['debug']['order_no'] = $order_no;

// 检查订单是否存在
$order_table = $DB->table('payment_orders');
$response['debug']['order_table'] = $order_table;

$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if (!$order) {
    $response['message'] = '订单不存在';
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

$response['debug']['order_status'] = $order['status'];
$response['debug']['order_expire'] = $order['expire_time'];
$response['debug']['current_time'] = time();

// 如果订单已经是已支付状态，直接返回成功
if ($order['status'] == 1) {
    $response['success'] = true;
    $response['paid'] = true;
    $response['message'] = '支付成功';
    $response['debug']['already_paid'] = true;
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查订单是否过期
if ($order['expire_time'] < time()) {
    $response['message'] = '订单已过期';
    $response['debug']['expired'] = true;
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查函数是否存在
if (!function_exists('check_payment_status')) {
    $response['message'] = 'check_payment_status 函数不存在';
    $response['debug']['function_missing'] = true;
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 查询第三方支付状态
$payment_result = check_payment_status($order_no);
$response['debug']['payment_result'] = $payment_result;

if ($payment_result['success']) {
    $response['success'] = true;
    
    if ($payment_result['paid']) {
        // 检查处理函数是否存在
        if (!function_exists('process_payment_success')) {
            $response['message'] = 'process_payment_success 函数不存在';
            $response['debug']['process_function_missing'] = true;
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 支付成功，处理业务逻辑
        $process_result = process_payment_success($order_no);
        $response['debug']['process_result'] = $process_result;
        
        if ($process_result) {
            $response['paid'] = true;
            $response['message'] = '支付成功，网站已提交审核';
        } else {
            $response['message'] = '支付成功，但处理失败，请联系客服';
        }
    } else {
        $response['message'] = '未支付';
    }
} else {
    $response['message'] = '查询支付状态失败: ' . $payment_result['message'];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
