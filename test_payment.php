<?php
/**
 * 测试支付功能
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

echo "<h2>测试支付功能</h2>";

// 测试引入支付函数文件
if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
    echo "<p style='color: green;'>✓ 支付函数文件加载成功</p>";
    
    // 测试函数是否存在
    $functions = ['generate_order_no', 'get_payment_price', 'create_payment_order', 'create_payment', 'check_payment_status', 'process_payment_success'];
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "<p style='color: green;'>✓ 函数 {$func} 存在</p>";
        } else {
            echo "<p style='color: red;'>✗ 函数 {$func} 不存在</p>";
        }
    }
    
    // 测试生成订单号
    if (function_exists('generate_order_no')) {
        $order_no = generate_order_no();
        echo "<p>测试订单号: {$order_no}</p>";
    }
    
    // 测试获取价格
    if (function_exists('get_payment_price')) {
        for ($i = 1; $i <= 3; $i++) {
            $price = get_payment_price($i);
            if ($price) {
                echo "<p>付费类型 {$i}: {$price['name']} - ¥{$price['price']}</p>";
            }
        }
    }
    
    // 测试数据库表
    $tables = ['payment_orders', 'payment_records'];
    foreach ($tables as $table_name) {
        $table = $DB->table($table_name);
        $result = $DB->query("SHOW TABLES LIKE '$table'");
        if ($DB->num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ 数据表 {$table} 存在</p>";
        } else {
            echo "<p style='color: red;'>✗ 数据表 {$table} 不存在</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>✗ 支付函数文件不存在</p>";
}

echo "<hr>";
echo "<p><a href='?mod=quicksubmit'>返回快速提交页面</a></p>";
?>
