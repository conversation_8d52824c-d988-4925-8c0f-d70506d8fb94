<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 支付处理函数模块
 * 用于快速提交的付费自动审核功能
 */

/**
 * 生成订单号
 * @return string 订单号
 */
function generate_order_no() {
    return date('Ymd') . substr(time(), -5) . substr(microtime(), 2, 5) . sprintf('%03d', rand(0, 999));
}

/**
 * 获取付费服务价格
 * @param int $payment_type 付费类型：1=VIP，2=推荐，3=快审
 * @return array|false 价格信息
 */
function get_payment_price($payment_type) {
    // 默认价格配置
    $default_prices = array(
        1 => array('price' => 50.00, 'name' => 'VIP收录', 'description' => 'VIP网站收录服务'),
        2 => array('price' => 20.00, 'name' => '推荐收录', 'description' => '推荐位网站收录服务'),
        3 => array('price' => 10.00, 'name' => '快审收录', 'description' => '快速审核收录服务')
    );
    
    if (!isset($default_prices[$payment_type])) {
        return false;
    }
    
    return $default_prices[$payment_type];
}

/**
 * 创建支付订单
 * @param array $form_data 表单数据
 * @param int $payment_type 付费类型
 * @return array|false 订单信息
 */
function create_payment_order($form_data, $payment_type) {
    global $DB;
    
    // 获取价格信息
    $price_info = get_payment_price($payment_type);
    if (!$price_info) {
        return false;
    }
    
    // 生成订单号
    $order_no = generate_order_no();
    
    // 订单过期时间（30分钟）
    $expire_time = time() + 1800;
    
    // 准备订单数据
    $order_data = array(
        'order_no' => $order_no,
        'payment_type' => $payment_type,
        'amount' => $price_info['price'],
        'status' => 0, // 待支付
        'web_name' => $form_data['web_name'],
        'web_url' => $form_data['web_url'],
        'web_tags' => $form_data['web_tags'],
        'web_intro' => $form_data['web_intro'],
        'web_owner' => $form_data['web_owner'],
        'web_email' => $form_data['web_email'],
        'cate_id' => $form_data['cate_id'],
        'expire_time' => $expire_time,
        'create_time' => time(),
        'update_time' => time()
    );
    
    // 插入订单
    $table = $DB->table('payment_orders');
    $DB->insert($table, $order_data);
    $order_id = $DB->insert_id();
    
    if ($order_id) {
        $order_data['order_id'] = $order_id;
        $order_data['price_info'] = $price_info;
        return $order_data;
    }
    
    return false;
}

/**
 * 调用支付接口创建支付订单
 * @param array $order_data 订单数据
 * @return array|false 支付接口返回结果
 */
function create_payment($order_data) {
    $api_url = 'https://api.rcku.cn/wxpay/pay';
    
    // 准备支付参数
    $params = array(
        'price' => $order_data['amount'],
        'orderid' => $order_data['order_no'],
        'description' => $order_data['price_info']['description']
    );
    
    // 构建请求URL
    $request_url = $api_url . '?' . http_build_query($params);
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 && $response) {
        $result = json_decode($response, true);
        if ($result && isset($result['ewm'])) {
            // 更新订单的二维码信息
            global $DB;
            $table = $DB->table('payment_orders');
            $update_data = array(
                'qr_code' => $result['ewm'],
                'update_time' => time()
            );
            $DB->update($table, $update_data, array('order_id' => $order_data['order_id']));
            
            return $result;
        }
    }
    
    return false;
}

/**
 * 查询支付结果
 * @param string $order_no 订单号
 * @return array 查询结果
 */
function check_payment_status($order_no) {
    $api_url = 'https://api.rcku.cn/wxpay/result';
    
    // 构建请求URL
    $request_url = $api_url . '?orderid=' . $order_no;
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $result = array(
        'success' => false,
        'paid' => false,
        'message' => '查询失败'
    );
    
    if ($http_code == 200 && $response) {
        $data = json_decode($response, true);
        if ($data) {
            $result['success'] = true;
            if (isset($data['status']) && $data['status'] == 1) {
                $result['paid'] = true;
                $result['message'] = '支付成功';
            } else {
                $result['message'] = '未支付';
            }
        }
    }
    
    return $result;
}

/**
 * 处理支付成功后的业务逻辑
 * @param string $order_no 订单号
 * @return bool 处理结果
 */
function process_payment_success($order_no) {
    global $DB;

    // 获取订单信息
    $order_table = $DB->table('payment_orders');
    $order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no' AND status = 0");

    if (!$order) {
        return false;
    }

    // 更新订单状态
    $update_data = array(
        'status' => 1, // 已支付
        'pay_time' => time(),
        'update_time' => time()
    );
    $DB->update($order_table, $update_data, array('order_id' => $order['order_id']));

    // 创建网站记录
    $web_data = array(
        'cate_id' => $order['cate_id'],
        'user_id' => 0, // 非会员用户ID为0
        'web_name' => $order['web_name'],
        'web_url' => $order['web_url'],
        'web_tags' => $order['web_tags'],
        'web_intro' => $order['web_intro'],
        'web_status' => 3, // 根据付费类型设置状态
        'web_ctime' => time()
    );

    // 根据付费类型设置网站属性
    switch ($order['payment_type']) {
        case 1: // VIP
            $web_data['web_ispay'] = 1;
            $web_data['web_vip_expire'] = time() + 365 * 24 * 3600; // 一年
            break;
        case 2: // 推荐
            $web_data['web_isbest'] = 1;
            $web_data['web_recommend_expire'] = time() + 30 * 24 * 3600; // 一个月
            break;
        case 3: // 快审
            $web_data['web_status'] = 3; // 直接审核通过
            break;
    }

    // 插入网站数据
    $websites_table = $DB->table('websites');
    $DB->insert($websites_table, $web_data);
    $web_id = $DB->insert_id();

    if ($web_id) {
        // 更新订单的web_id
        $DB->update($order_table, array('web_id' => $web_id), array('order_id' => $order['order_id']));

        // 插入统计数据
        $web_ip_numeric = sprintf("%u", ip2long(get_client_ip()));
        $stat_data = array(
            'web_id' => $web_id,
            'web_ip' => $web_ip_numeric,
            'web_grank' => 0,
            'web_brank' => 0,
            'web_srank' => 0,
            'web_arank' => 0,
            'web_utime' => time()
        );
        $DB->insert($DB->table('webdata'), $stat_data);

        // 更新分类统计
        $DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=".$order['cate_id']);

        // 记录付费记录
        $payment_data = array(
            'web_id' => $web_id,
            'web_name' => $order['web_name'],
            'web_url' => $order['web_url'],
            'payment_type' => $order['payment_type'],
            'payment_amount' => $order['amount'],
            'payment_time' => time(),
            'expire_time' => isset($web_data['web_vip_expire']) ? $web_data['web_vip_expire'] : (isset($web_data['web_recommend_expire']) ? $web_data['web_recommend_expire'] : 0),
            'operator' => 'system',
            'status' => 1,
            'remark' => '快速提交付费自动审核',
            'created_at' => time()
        );
        $DB->insert($DB->table('payment_records'), $payment_data);

        return true;
    }

    return false;
}
?>
