<?php
/**
 * 手动检查支付状态
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
}

echo "<h2>检查支付状态</h2>";

// 获取订单号
$order_no = isset($_GET['order_no']) ? trim($_GET['order_no']) : '202508076566030230404';

echo "<h3>1. 订单信息</h3>";
echo "<p>订单号: {$order_no}</p>";

// 获取订单信息
$order_table = $DB->table('payment_orders');
$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if ($order) {
    echo "<p style='color: green;'>✓ 订单存在</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    foreach ($order as $key => $value) {
        if ($key == 'qr_code' && strlen($value) > 50) {
            $value = substr($value, 0, 50) . '...';
        }
        echo "<tr><td><strong>{$key}</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>2. 当前订单状态</h3>";
    echo "<p>数据库状态: " . ($order['status'] == 0 ? '待支付' : ($order['status'] == 1 ? '已支付' : '已取消')) . "</p>";
    echo "<p>过期时间: " . date('Y-m-d H:i:s', $order['expire_time']) . "</p>";
    echo "<p>是否过期: " . ($order['expire_time'] < time() ? '是' : '否') . "</p>";
    
    echo "<h3>3. 查询第三方支付状态</h3>";
    
    if (function_exists('check_payment_status')) {
        $payment_result = check_payment_status($order_no);
        echo "<p>查询结果:</p>";
        echo "<pre>";
        print_r($payment_result);
        echo "</pre>";
        
        if ($payment_result['success'] && $payment_result['paid']) {
            echo "<p style='color: green;'>✓ 第三方确认已支付</p>";
            
            if ($order['status'] == 0) {
                echo "<h3>4. 处理支付成功业务逻辑</h3>";
                
                if (function_exists('process_payment_success')) {
                    $process_result = process_payment_success($order_no);
                    
                    if ($process_result) {
                        echo "<p style='color: green;'>✓ 支付成功处理完成</p>";
                        
                        // 重新获取订单状态
                        $updated_order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");
                        echo "<p>更新后状态: " . ($updated_order['status'] == 1 ? '已支付' : '待支付') . "</p>";
                        
                        // 检查是否创建了网站记录
                        if ($updated_order['web_id'] > 0) {
                            $websites_table = $DB->table('websites');
                            $website = $DB->fetch_one("SELECT * FROM $websites_table WHERE web_id = " . $updated_order['web_id']);
                            if ($website) {
                                echo "<p style='color: green;'>✓ 网站记录已创建</p>";
                                echo "<p>网站ID: {$website['web_id']}</p>";
                                echo "<p>网站名称: {$website['web_name']}</p>";
                                echo "<p>网站状态: " . ($website['web_status'] == 3 ? '已审核' : '待审核') . "</p>";
                                
                                // 根据付费类型显示特殊状态
                                switch ($order['payment_type']) {
                                    case 1:
                                        echo "<p>VIP状态: " . ($website['web_ispay'] == 1 ? '是' : '否') . "</p>";
                                        if (isset($website['web_vip_expire'])) {
                                            echo "<p>VIP到期: " . date('Y-m-d H:i:s', $website['web_vip_expire']) . "</p>";
                                        }
                                        break;
                                    case 2:
                                        echo "<p>推荐状态: " . ($website['web_isbest'] == 1 ? '是' : '否') . "</p>";
                                        if (isset($website['web_recommend_expire'])) {
                                            echo "<p>推荐到期: " . date('Y-m-d H:i:s', $website['web_recommend_expire']) . "</p>";
                                        }
                                        break;
                                    case 3:
                                        echo "<p>快审状态: 已审核通过</p>";
                                        break;
                                }
                            } else {
                                echo "<p style='color: red;'>✗ 网站记录未找到</p>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ 未创建网站记录</p>";
                        }
                        
                    } else {
                        echo "<p style='color: red;'>✗ 支付成功处理失败</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ process_payment_success 函数不存在</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ 订单已经处理过了</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ 第三方显示未支付或查询失败</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ check_payment_status 函数不存在</p>";
    }
    
    echo "<h3>5. 手动测试第三方API</h3>";
    $api_url = 'https://api.rcku.cn/wxpay/result?orderid=' . $order_no;
    echo "<p>API URL: <a href='{$api_url}' target='_blank'>{$api_url}</a></p>";
    
    // 手动调用API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p>HTTP状态码: {$http_code}</p>";
    echo "<p>API响应:</p>";
    echo "<pre style='background: #f5f5f5; padding: 10px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data) {
            echo "<p>解析结果:</p>";
            echo "<pre>";
            print_r($data);
            echo "</pre>";
        }
    }
    
} else {
    echo "<p style='color: red;'>✗ 订单不存在</p>";
}

echo "<hr>";
echo "<p><a href='?mod=payment_check&order_no={$order_no}'>测试支付检查接口</a></p>";
echo "<p><a href='?mod=payment&order_no={$order_no}'>返回支付页面</a></p>";
?>
