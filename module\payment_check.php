<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 引入支付处理函数
require_once(APP_PATH.'module/payment_functions.php');

// 获取订单号
$order_no = trim($_GET['order_no']);

$response = array(
    'success' => false,
    'paid' => false,
    'message' => '参数错误'
);

if (empty($order_no)) {
    echo json_encode($response);
    exit;
}

// 检查订单是否存在
$order_table = $DB->table('payment_orders');
$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if (!$order) {
    $response['message'] = '订单不存在';
    echo json_encode($response);
    exit;
}

// 如果订单已经是已支付状态，直接返回成功
if ($order['status'] == 1) {
    $response['success'] = true;
    $response['paid'] = true;
    $response['message'] = '支付成功';
    echo json_encode($response);
    exit;
}

// 检查订单是否过期
if ($order['expire_time'] < time()) {
    $response['message'] = '订单已过期';
    echo json_encode($response);
    exit;
}

// 查询第三方支付状态
$payment_result = check_payment_status($order_no);

if ($payment_result['success']) {
    $response['success'] = true;
    
    if ($payment_result['paid']) {
        // 支付成功，处理业务逻辑
        $process_result = process_payment_success($order_no);
        
        if ($process_result) {
            $response['paid'] = true;
            $response['message'] = '支付成功，网站已提交审核';
        } else {
            $response['message'] = '支付成功，但处理失败，请联系客服';
        }
    } else {
        $response['message'] = '未支付';
    }
} else {
    $response['message'] = '查询支付状态失败';
}

echo json_encode($response);
?>
