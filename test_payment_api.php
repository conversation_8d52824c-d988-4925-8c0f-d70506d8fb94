<?php
/**
 * 测试支付API接口
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require_once(MOD_PATH.'payment_functions.php');

echo "<h2>测试支付API接口</h2>";

// 测试数据
$test_order_no = generate_order_no();
$test_price = 10.00;
$test_description = '快速审核收录服务';

echo "<h3>1. 测试创建支付订单</h3>";
echo "<p>订单号: {$test_order_no}</p>";
echo "<p>价格: ¥{$test_price}</p>";
echo "<p>描述: {$test_description}</p>";

// 调用支付接口
$api_url = 'https://api.rcku.cn/wxpay/pay';
$params = array(
    'price' => $test_price,
    'orderid' => $test_order_no,
    'description' => $test_description
);

$request_url = $api_url . '?' . http_build_query($params);
echo "<p>请求URL: <a href='{$request_url}' target='_blank'>{$request_url}</a></p>";

// 发送请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $request_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>2. 支付接口响应</h3>";
echo "<p>HTTP状态码: {$http_code}</p>";
if ($error) {
    echo "<p style='color: red;'>CURL错误: {$error}</p>";
}

echo "<p>响应内容:</p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($response);
echo "</pre>";

if ($http_code == 200 && $response) {
    $result = json_decode($response, true);
    if ($result) {
        echo "<h3>3. 解析结果</h3>";
        echo "<pre>";
        print_r($result);
        echo "</pre>";
        
        if (isset($result['ewm'])) {
            echo "<h3>4. 二维码内容</h3>";
            echo "<p>二维码数据: {$result['ewm']}</p>";
            
            // 生成二维码显示
            echo "<div id='qrcode'></div>";
            echo "<script src='https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'></script>";
            echo "<script>
                QRCode.toCanvas(document.getElementById('qrcode'), '{$result['ewm']}', {
                    width: 200,
                    height: 200,
                    margin: 2
                });
            </script>";
        }
    }
}

echo "<h3>5. 测试查询支付结果</h3>";
$check_url = 'https://api.rcku.cn/wxpay/result?orderid=' . $test_order_no;
echo "<p>查询URL: <a href='{$check_url}' target='_blank'>{$check_url}</a></p>";

// 查询支付结果
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $check_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$check_response = curl_exec($ch);
$check_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p>查询响应:</p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($check_response);
echo "</pre>";

if ($check_http_code == 200 && $check_response) {
    $check_result = json_decode($check_response, true);
    if ($check_result) {
        echo "<p>解析结果:</p>";
        echo "<pre>";
        print_r($check_result);
        echo "</pre>";
    }
}

echo "<hr>";
echo "<p><a href='?mod=quicksubmit'>返回快速提交页面</a></p>";
?>
