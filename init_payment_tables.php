<?php
/**
 * 初始化支付相关数据表
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

require(APP_PATH.'init.php');

echo "<h2>初始化支付相关数据表</h2>";

try {
    // 创建支付订单表
    $payment_orders_table = $DB->table('payment_orders');
    $create_orders_sql = "CREATE TABLE IF NOT EXISTS `{$payment_orders_table}` (
        `order_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `user_id` int(10) unsigned NOT NULL DEFAULT '0',
        `web_id` int(10) unsigned NOT NULL DEFAULT '0',
        `order_no` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
        `trade_no` varchar(64) NOT NULL DEFAULT '' COMMENT '第三方交易号',
        `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
        `pay_type` varchar(20) NOT NULL DEFAULT 'wxpay' COMMENT '支付方式',
        `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
        `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '支付状态：0=待支付，1=已支付，2=已取消',
        `web_name` varchar(100) NOT NULL DEFAULT '' COMMENT '网站名称',
        `web_url` varchar(255) NOT NULL DEFAULT '' COMMENT '网站地址',
        `web_tags` varchar(100) NOT NULL DEFAULT '' COMMENT '网站标签',
        `web_intro` text NOT NULL COMMENT '网站简介',
        `web_owner` varchar(50) NOT NULL DEFAULT '' COMMENT '站长姓名',
        `web_email` varchar(50) NOT NULL DEFAULT '' COMMENT '联系邮箱',
        `cate_id` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
        `qr_code` text NOT NULL COMMENT '支付二维码内容',
        `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单过期时间',
        `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
        `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付时间',
        `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`order_id`),
        UNIQUE KEY `order_no` (`order_no`),
        KEY `user_id` (`user_id`),
        KEY `web_id` (`web_id`),
        KEY `status` (`status`),
        KEY `create_time` (`create_time`),
        KEY `expire_time` (`expire_time`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付订单表'";
    
    if ($DB->query($create_orders_sql)) {
        echo "<p style='color: green;'>✓ 支付订单表创建成功</p>";
    } else {
        echo "<p style='color: red;'>✗ 支付订单表创建失败</p>";
    }
    
    // 检查websites表是否有付费相关字段
    $websites_table = $DB->table('websites');
    
    // 检查字段是否存在
    $check_fields = array(
        'web_vip_expire' => 'VIP到期时间',
        'web_recommend_expire' => '推荐到期时间',
        'web_fast_expire' => '快审到期时间'
    );
    
    foreach ($check_fields as $field => $desc) {
        $check_sql = "SHOW COLUMNS FROM `{$websites_table}` LIKE '{$field}'";
        $result = $DB->query($check_sql);
        
        if ($DB->num_rows($result) == 0) {
            // 字段不存在，添加字段
            $alter_sql = "ALTER TABLE `{$websites_table}` ADD COLUMN `{$field}` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '{$desc}'";
            if ($DB->query($alter_sql)) {
                echo "<p style='color: green;'>✓ 网站表添加{$desc}字段成功</p>";
            } else {
                echo "<p style='color: red;'>✗ 网站表添加{$desc}字段失败</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ 网站表{$desc}字段已存在</p>";
        }
    }
    
    // 检查payment_records表是否存在
    $payment_records_table = $DB->table('payment_records');
    $check_records = $DB->query("SHOW TABLES LIKE '$payment_records_table'");
    
    if ($DB->num_rows($check_records) == 0) {
        $create_records_sql = "CREATE TABLE IF NOT EXISTS `{$payment_records_table}` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `web_id` int(10) unsigned NOT NULL DEFAULT '0',
            `web_name` varchar(100) NOT NULL DEFAULT '',
            `web_url` varchar(255) NOT NULL DEFAULT '',
            `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
            `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付费金额',
            `payment_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付费时间',
            `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '到期时间',
            `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
            `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已过期',
            `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
            `created_at` int(10) unsigned NOT NULL DEFAULT '0',
            PRIMARY KEY (`id`),
            KEY `web_id` (`web_id`),
            KEY `payment_type` (`payment_type`),
            KEY `payment_time` (`payment_time`),
            KEY `expire_time` (`expire_time`),
            KEY `status` (`status`)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
        
        if ($DB->query($create_records_sql)) {
            echo "<p style='color: green;'>✓ 付费记录表创建成功</p>";
        } else {
            echo "<p style='color: red;'>✗ 付费记录表创建失败</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ 付费记录表已存在</p>";
    }
    
    echo "<h3>初始化完成！</h3>";
    echo "<p><a href='?mod=quicksubmit'>前往快速提交页面测试</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
